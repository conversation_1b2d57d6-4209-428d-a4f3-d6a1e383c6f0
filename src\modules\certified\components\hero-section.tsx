import { Badge } from '@/components/ui/badge'
import { Award, Shield } from 'lucide-react'
import React from 'react'

const HeroSection = () => {
  return (
    <div>
        <section className="relative w-full py-16 md:py-24 bg-gradient-to-br from-secondary via-secondary/90 to-secondary/80">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="container mx-auto relative px-4">
            <div className="flex flex-col items-center space-y-6 text-center text-white">
              <div className="flex items-center space-x-2">
                <Shield className="h-12 w-12 text-blue-300" />
                <Award className="h-12 w-12 text-blue-300" />
              </div>
              <div className="space-y-4 max-w-4xl">
                <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl">
                  Certifications & Compliance
                </h1>
                <p className="mx-auto max-w-3xl text-xl text-blue-100 md:text-2xl">
                  Maintaining the highest standards of aviation safety, quality, and regulatory compliance across all
                  operations
                </p>
              </div>
              <Badge className="bg-blue-500/20 text-blue-100 border-blue-400/30 text-lg px-4 py-2">
                CAAN Approved NCAR D3.3 Supplier Organization
              </Badge>
            </div>
          </div>
        </section>
    </div>
  )
}

export default HeroSection