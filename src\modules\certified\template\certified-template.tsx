import HeroSection from "../components/hero-section";
import PrimaryCertification from "../components/primary-certification";
import QualityManagement from "../components/quality-management";
import InternationalStandard from "../components/international-standard";
import Documentation from "../components/documentation";

export default function CertifiedPage() {
    return (
        <div className="flex flex-col min-h-screen bg-white">

            <main className=" flex-1">
                <HeroSection />
                <PrimaryCertification />
                <QualityManagement />
                <InternationalStandard />
                <Documentation />
                {/* <section className="w-full py-16 md:py-24 bg-blue-900 text-white">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center space-y-8">
              <div className="space-y-4">
                <Badge className="bg-blue-700 text-blue-100">Our Commitment</Badge>
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                  Unwavering Commitment to Compliance & Safety
                </h2>
                <p className="text-xl text-blue-100 md:text-2xl">
                  Every component is handled with meticulous attention to certification and traceability
                </p>
              </div>
              <div className="grid gap-8 md:grid-cols-3">
                <div className="space-y-4">
                  <div className="w-16 h-16 bg-blue-700 rounded-full flex items-center justify-center mx-auto">
                    <Lock className="h-8 w-8 text-blue-200" />
                  </div>
                  <h3 className="text-xl font-bold">Strict Compliance</h3>
                  <p className="text-blue-100">Adherence to CAAN regulations, ICAO standards, and EASA practices</p>
                </div>
                <div className="space-y-4">
                  <div className="w-16 h-16 bg-blue-700 rounded-full flex items-center justify-center mx-auto">
                    <Users className="h-8 w-8 text-blue-200" />
                  </div>
                  <h3 className="text-xl font-bold">Trained Personnel</h3>
                  <p className="text-blue-100">Dedicated team of trained Quality Control Management professionals</p>
                </div>
                <div className="space-y-4">
                  <div className="w-16 h-16 bg-blue-700 rounded-full flex items-center justify-center mx-auto">
                    <BookOpen className="h-8 w-8 text-blue-200" />
                  </div>
                  <h3 className="text-xl font-bold">Continuous Training</h3>
                  <p className="text-blue-100">Regular updates on regulatory changes and industry best practices</p>
                </div>
              </div>
              <div className="pt-8">
                <Button size="lg" className="bg-white text-blue-900 hover:bg-gray-100">
                  <Download className="h-5 w-5 mr-2" />
                  Download Certification Documents
                </Button>
              </div>
            </div>
          </div>
        </section> */}
            </main>
        </div>
    )
}
