import { notFound } from "next/navigation";
import getBlogbySlug from "@/actions/blog/get-blog-by-slug";
import type { IBlogPost } from "@/types/blog";
import Image from "next/image";

type Params = { slug: string };

export default async function NewsPostPage(
    props: { params: Promise<Params> }
) {
    const { slug } = await props.params;

    const data = await getBlogbySlug(slug);

    if (!data?.success || !data.data) {
        notFound();
    }

    const post: IBlogPost = data.data;

    return (
        <article className="container mx-auto py-10 px-4">
            <h1 className="text-3xl font-bold mb-4">{post.title}</h1>
            <p className="text-sm text-muted-foreground mb-8">
                {new Date(post.createdAt).toLocaleDateString(undefined, {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                })}
            </p>
            {(post.image) && (
                <Image
                    src={post.image || "/images/helicopter.png"}
                    alt={post.title}
                    width={200}
                    height={100}
                    className="w-full mb-6"
                />
            )}
            <div
                className="prose max-w-none"
                dangerouslySetInnerHTML={{ __html: post.content }}
            />
        </article>
    );
}
