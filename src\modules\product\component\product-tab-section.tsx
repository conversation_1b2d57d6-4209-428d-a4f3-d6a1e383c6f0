import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import type { IProductTab } from "@/types/category";

interface ProductTabsSectionProps {
  tabs?: IProductTab[];
}

export default function ProductTabsSection({ tabs }: ProductTabsSectionProps) {
  if (!tabs || tabs.length === 0) return <p>No tabs available.</p>;

  return (
    <section className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
      {tabs.map((tab) => (
        <div key={tab.id} className="mb-6">
          <h2 className="text-xl font-semibold mb-4 flex items-center">
            <span className="w-1.5 h-6 bg-primary mr-2 inline-block rounded" />
            {tab.name}
          </h2>

          {tab.items && tab.items.length > 0 ? (
            <ul className="list-disc list-inside space-y-3 text-gray-700">
              <Accordion type="single" collapsible className="space-y-2 text-white">
                {tab.items.map((item) => (
                  <AccordionItem key={item.id} value={item.id}>
                    <AccordionTrigger className="bg-secondary rounded-none text-xl text-white font-semibold px-4 py-1">
                      {item.title}
                    </AccordionTrigger>
                    <AccordionContent className="prose prose-sm text-base px-6 text-gray-700">
                      <div dangerouslySetInnerHTML={{ __html: item.description }} />
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </ul>
          ) : (
            <p className="text-gray-500">No details available for this tab.</p>
          )}
        </div>
      ))}
    </section>


    // <section className="mt-8">
    //   {tabs.map((tab) => (
    //     <div key={tab.id} className="mb-6">
    //       <h2 className="text-xl font-semibold mb-4 flex items-center">
    //         <span className="w-1.5 h-6 bg-primary mr-2 inline-block rounded" />
    //         {tab.name}
    //       </h2>

    //       {tab.items && tab.items.length > 0 ? (
    //         <ul className="list-disc list-inside space-y-3 text-gray-700">
    //           <Accordion type="single" collapsible className="space-y-2 text-white">
    //             {tab.items.map((item) => (
    //               <AccordionItem key={item.id} value={item.id}>
    //                 <AccordionTrigger className="bg-secondary rounded-none text-xl text-white font-semibold px-4 py-1">
    //                   {item.title}
    //                 </AccordionTrigger>
    //                 <AccordionContent className="prose prose-sm text-base px-6 text-gray-700">
    //                   <div dangerouslySetInnerHTML={{ __html: item.description }} />
    //                 </AccordionContent>
    //               </AccordionItem>
    //             ))}
    //           </Accordion>
    //         </ul>
    //       ) : (
    //         <p className="text-gray-500">No details available for this tab.</p>
    //       )}
    //     </div>
    //   ))}
    // </section>
  );
}
