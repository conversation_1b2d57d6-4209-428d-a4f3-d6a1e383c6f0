import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Check<PERSON>ircle, Shield, Verified } from 'lucide-react'
import React from 'react'

const PrimaryCertification = () => {
  return (
    <div>
        <section className="w-full py-16 ">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <Card className="border-2 border-blue-200 shadow-xl bg-gradient-to-br from-blue-50 to-white">
                <CardHeader className="text-center pb-8">
                  <div className="mx-auto w-24 h-24 bg-secondary/80 rounded-full flex items-center justify-center mb-6">
                    <Shield className="h-12 w-12 text-white" />
                  </div>
                  <CardTitle className="text-3xl font-bold text-secondary">
                    CAAN NCAR D3.3 Supplier Organization
                  </CardTitle>
                  <CardDescription className="text-lg text-gray-600 mt-4">
                    Civil Aviation Authority of Nepal - Primary Aviation Parts Supplier Certification
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6 text-center">
                  <div className="grid gap-6 md:grid-cols-2">
                    <div className="space-y-4">
                      <h3 className="text-xl font-semibold text-gray-900">Certification Details</h3>
                      <div className="space-y-2 ml-20">
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="h-5 w-5 text-green-600" />
                          <span className="text-sm">Regulatory Authority: CAAN Nepal</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="h-5 w-5 text-green-600" />
                          <span className="text-sm">Certification Type: NCAR D3.3</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="h-5 w-5 text-green-600" />
                          <span className="text-sm">Status: Active & Current</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="h-5 w-5 text-green-600" />
                          <span className="text-sm">Scope: Aviation Parts Supply</span>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <h3 className="text-xl font-semibold text-gray-900">Authorized Activities</h3>
                      <div className="space-y-2 ml-20">
                        <div className="flex items-center space-x-2">
                          <Verified className="h-5 w-5 text-secondary" />
                          <span className="text-sm">Aircraft Spare Parts Supply</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Verified className="h-5 w-5 text-secondary" />
                          <span className="text-sm">Parts Certification & Documentation</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Verified className="h-5 w-5 text-secondary" />
                          <span className="text-sm">Quality Control & Inspection</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Verified className="h-5 w-5 text-secondary" />
                          <span className="text-sm">Logistics & Distribution</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
    </div>
  )
}

export default PrimaryCertification