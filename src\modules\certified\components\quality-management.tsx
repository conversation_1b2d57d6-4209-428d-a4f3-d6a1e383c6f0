import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert<PERSON>riangle, CheckCircle, ClipboardCheck } from 'lucide-react'
import React from 'react'

const QualityManagement = () => {
    return (
        <div>
            <section className="w-full py-16  bg-gray-50">
                <div className="container mx-auto px-4">
                    <div className="text-center space-y-4 mb-12">
                        <Badge className="bg-primary/10 text-secondary">Quality Management</Badge>
                        <h2 className="text-4xl text-center font-bold bg-gradient-to-r from-secondary to-primary bg-clip-text text-transparent mb-2">
                            Quality & Safety Management Systems
                        </h2>
                        <p className="mx-auto max-w-3xl text-gray-600 md:text-xl">
                            Our integrated management systems ensure consistent quality, safety, and compliance across all
                            operations
                        </p>
                    </div>
                    <div className="grid gap-8 md:grid-cols-2">
                        <Card className="border-0 shadow-lg">
                            <CardHeader>
                                <div className="flex items-center space-x-3">
                                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                        <ClipboardCheck className="h-6 w-6 text-green-600" />
                                    </div>
                                    <div>
                                        <CardTitle className="text-xl">Quality Management System (QMS)</CardTitle>
                                        <CardDescription>EASA-Influenced Standards</CardDescription>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <p className="text-gray-600">
                                    Our QMS is built on EASA-influenced standards, ensuring full part traceability, airworthiness
                                    compliance, and consistent quality control processes.
                                </p>
                                <div className="space-y-2">
                                    <div className="flex items-center space-x-2">
                                        <CheckCircle className="h-4 w-4 text-green-600" />
                                        <span className="text-sm">Full Part Traceability</span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <CheckCircle className="h-4 w-4 text-green-600" />
                                        <span className="text-sm">Airworthiness Compliance</span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <CheckCircle className="h-4 w-4 text-green-600" />
                                        <span className="text-sm">Continuous Improvement</span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <CheckCircle className="h-4 w-4 text-green-600" />
                                        <span className="text-sm">Document Control</span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-lg">
                            <CardHeader>
                                <div className="flex items-center space-x-3">
                                    <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                        <AlertTriangle className="h-6 w-6 text-orange-600" />
                                    </div>
                                    <div>
                                        <CardTitle className="text-xl">Safety Management System (SMS)</CardTitle>
                                        <CardDescription>ICAO Annex 19 Compliant</CardDescription>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <p className="text-gray-600">
                                    Our SMS follows ICAO Annex 19 standards, providing systematic approach to managing safety risks and
                                    ensuring operational safety.
                                </p>
                                <div className="space-y-2">
                                    <div className="flex items-center space-x-2">
                                        <CheckCircle className="h-4 w-4 text-green-600" />
                                        <span className="text-sm">Risk Assessment & Management</span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <CheckCircle className="h-4 w-4 text-green-600" />
                                        <span className="text-sm">Safety Performance Monitoring</span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <CheckCircle className="h-4 w-4 text-green-600" />
                                        <span className="text-sm">Incident Reporting System</span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <CheckCircle className="h-4 w-4 text-green-600" />
                                        <span className="text-sm">Safety Training Programs</span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </section>
        </div>
    )
}

export default QualityManagement