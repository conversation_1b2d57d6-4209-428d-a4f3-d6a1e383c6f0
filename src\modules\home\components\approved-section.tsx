'use client';

import { ChevronLeft, ChevronRight } from 'lucide-react';
import Image from 'next/image';
import { useCallback, useEffect, useState } from 'react';

const logos = [
  {
    src: '/images/approved/buddha-air.jpg',
    alt: 'Buddha Air',
  },
  {
    src: '/images/approved/mro.png',
    alt: 'MRO',
  },
  {
    src: '/images/approved/nepal-army-logo.jpg',
    alt: 'Nepal Army',
  },
  {
    src: '/images/approved/mustang-helicopter.jpeg',
    alt: 'Mustang Helicopter',
  }
];

const ApprovedSuppliersSection = () => {
  const slidesToShow = 3;
  const maxIndex = Math.max(logos.length - slidesToShow, 0);

  const [currentSlide, setCurrentSlide] = useState(0);

  const nextSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev >= maxIndex ? 0 : prev + 1));
  }, [maxIndex]);

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev <= 0 ? maxIndex : prev - 1));
  };

  useEffect(() => {
    const interval = setInterval(() => {
      nextSlide();
    }, 4000);
    return () => clearInterval(interval);
  }, [nextSlide]);

  return (
    <section className="py-8 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-8">

          <div className="lg:w-1/2">
            <h2 className="text-xl text-start md:text-2xl lg:text-3xl text-gray-700 mb-2">
              Approved Suppliers to National Carriers, Major MROs, Lessors and Government Operators. We are trusted partners with top aviation organizations worldwide.

            </h2>
            <p className="mt-2 text-gray-600">
            </p>
          </div>

          <div className="lg:w-1/2 relative overflow-hidden">
            <div
              className="flex transition-transform duration-700 ease-in-out gap-x-2"
              style={{ transform: `translateX(-${(100 / slidesToShow) * currentSlide}%)` }}
            >
              {logos.map((logo, index) => (
                <div
                  key={index}
                  className="min-w-[33.3333%] flex justify-center p-4"
                >
                  <Image
                    src={logo.src}
                    alt={logo.alt}
                    width={150}
                    height={100}
                    className="hover:scale-105 object-contain transition-transform duration-200"
                  />
                </div>
              ))}
            </div>

            <button
              onClick={prevSlide}
              className="absolute left-0 top-1/2 -translate-y-1/2 bg-white/30 hover:bg-white/50 backdrop-blur-sm p-2 rounded-full"
              aria-label="Previous slide"
            >
              <ChevronLeft className="h-6 w-6 text-gray-800" />
            </button>
            <button
              onClick={nextSlide}
              className="absolute right-0 top-1/2 -translate-y-1/2 bg-white/30 hover:bg-white/50 backdrop-blur-sm p-2 rounded-full"
              aria-label="Next slide"
            >
              <ChevronRight className="h-6 w-6 text-gray-800" />
            </button>

            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2">
              {Array.from({ length: maxIndex + 1 }).map((_, idx) => (
                <button
                  key={idx}
                  onClick={() => setCurrentSlide(idx)}
                  className={`h-2 rounded-full transition-all ${currentSlide === idx ? 'w-12 bg-gray-700' : 'w-2 bg-gray-400/50'
                    }`}
                  aria-label={`Go to slide set ${idx + 1}`}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ApprovedSuppliersSection;
