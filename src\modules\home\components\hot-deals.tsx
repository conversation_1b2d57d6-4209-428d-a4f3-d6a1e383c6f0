'use client';

import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';

const hotDeals = [
  { id: '1', name: 'LH MAIN WINDSHIELD', price: '$2,450' },
  { id: '2', name: 'TOP ASSEMBLY', price: '$1,850' },
  { id: '3', name: 'ENGINE', price: '$10,000' },
  { id: '4', name: 'AIR SEPARATOR', price: '$3,200' },
  { id: '5', name: 'ANTENNA', price: '$750' },
  { id: '6', name: 'HYDRAULIC FILTER', price: '$425' },
  { id: '7', name: 'ENGINE', price: '$10,000' },
  { id: '8', name: 'PART 2', price: '$1,000' },
  { id: '9', name: 'PART 3', price: '$7,000' },
  { id: '10', name: 'PART 4', price: '$1,500' },
];

export default function HotDeals() {
  const scrollRef = useRef<HTMLDivElement>(null);
  const itemRef = useRef<HTMLDivElement>(null);

  const scrollLeft = () => {
    if (scrollRef.current && itemRef.current) {
      scrollRef.current.scrollBy({
        left: -itemRef.current.offsetWidth,
        behavior: 'smooth',
      });
    }
  };

  const scrollRight = () => {
    if (scrollRef.current && itemRef.current) {
      const container = scrollRef.current;
      const scrollAmount = itemRef.current.offsetWidth;

      if (container.scrollLeft + container.clientWidth >= container.scrollWidth - 5) {
        container.scrollTo({ left: 0, behavior: 'smooth' });
      } else {
        container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
      }
    }
  };

  useEffect(() => {
    const interval = setInterval(() => {
      scrollRight();
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  return (
    <section className="bg-gray-200 text-white py-3">
      <div className="container mx-auto px-2 md:px-4">
        {/* Desktop layout - remains horizontal */}
        <div className="hidden md:flex items-center gap-4">
          <span className="font-bold text-sm lg:text-base tracking-wide text-secondary">
            HOT DEALS:
          </span>

          <Button
            variant="ghost"
            size="sm"
            onClick={scrollLeft}
            className="text-gray-600 hover:bg-secondary hover:text-white transition-colors"
            aria-label="Previous deal"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>

          <div
            ref={scrollRef}
            className="flex-1 flex gap-1 md:gap-4 overflow-x-auto scroll-smooth scrollbar-none"
            style={{
              scrollbarWidth: 'none',
              msOverflowStyle: 'none',
            }}
          >
            {hotDeals.map((deal, idx) => (
              <div
                key={deal.id}
                ref={idx === 0 ? itemRef : undefined}
                className="flex items-center gap-3 rounded px-4 py-2 flex-shrink-0"
              >
                <span className="text-sm text-secondary">{deal.name}</span>
                <span className="font-bold text-secondary">{deal.price}</span>
              </div>
            ))}
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={scrollRight}
            className="text-gray-600 hover:bg-secondary hover:text-white transition-colors"
            aria-label="Next deal"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        {/* Mobile layout - column view */}
        <div className="md:hidden flex flex-col gap-3">
          <span className="font-bold text-sm tracking-wide text-secondary text-center">
            HOT DEALS:
          </span>

          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={scrollLeft}
              className="text-gray-600 hover:bg-secondary hover:text-white transition-colors"
              aria-label="Previous deal"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            <div
              ref={scrollRef}
              className="flex-1 flex gap-4 overflow-x-auto scroll-smooth scrollbar-none"
              style={{
                scrollbarWidth: 'none',
                msOverflowStyle: 'none',
              }}
            >
              {hotDeals.map((deal, idx) => (
                <div
                  key={deal.id}
                  ref={idx === 0 ? itemRef : undefined}
                  className="flex items-center gap-3 rounded px-4 py-2 flex-shrink-0"
                >
                  <span className="text-sm text-secondary">{deal.name}</span>
                  <span className="font-bold text-secondary">{deal.price}</span>
                </div>
              ))}
            </div>

            <Button
              variant="ghost"
              size="sm"
              onClick={scrollRight}
              className="text-gray-600 hover:bg-secondary hover:text-white transition-colors"
              aria-label="Next deal"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}