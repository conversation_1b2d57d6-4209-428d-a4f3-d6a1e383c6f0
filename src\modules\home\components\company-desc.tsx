'use client'

import React from 'react'
import { Plane } from 'lucide-react'

const CompanyDescription = () => {
  return (
    <section className="relative py-10 px-6 overflow-hidden">
      {/* <div className="absolute inset-0 opacity-30 bg-[url('/images/download.jpeg')] bg-center"></div> */}

      <div className="container mx-auto relative z-10 text-center">
        <div className="flex justify-center mb-6">
          <div className="w-16 h-16 flex items-center justify-center rounded-full bg-blue-100 text-secondary shadow-md">
            <Plane size={32} />
          </div>
        </div>

        <h1 className="text-xl md:text-2xl lg:text-3xl text-gray-700 leading-relaxed mb-6">
          Supporting <span className="font-semibold text-secondary">Helicopter</span>, Regional Aircraft,
          Defense Aircraft, wide body and narrow Aircraft across Nepal and South Asia , Mustang Airworks trusted Parts supplier and delivers Approved parts, lubricants, tires, and technical consulting & support-
        </h1>

        {/* <div className="mt-10 flex justify-center">
          <span className="inline-block h-1 w-24 bg-gradient-to-r from-blue-500 to-blue-300 rounded-full"></span>
        </div> */}
      </div>
    </section>
  )
}

export default CompanyDescription






// 'use client'

// import React from 'react'

// const CompanyDescription = () => {
//   return (
//     <section className="bg-gradient-to-br from-gray-50 to-gray-100 py-20 px-6">
//         <div className="container mx-auto text-center">
//           <h1 className="text-2xl md:text-3xl lg:text-4xl font-light text-gray-500 leading-tight">
//             Supporting Helicopter, Regional Aircraft, Defense aircraft, wide body and narrow Aircraft across Nepal and South Asia, Mustang Airworks trusted Parts supplier and delivers Approved parts, lubricants, tires, and technical consulting & support-
//           </h1>
//         </div>
//       </section>
//   )
// }

// export default CompanyDescription
