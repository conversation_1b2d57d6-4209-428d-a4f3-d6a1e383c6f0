
import getBlogs from "@/actions/blog/get-all-blogs";
import getCategory from "@/actions/category/get-category";
import getSubCategory from "@/actions/subcategory/get-subcategory";
import { IBlogPost } from "@/types/blog";
import { ICategory, IProduct, ISubCategory } from "@/types/category";
import type { MetadataRoute } from "next";


export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
    const baseUrl = "https://mustangairworks.com";

    const [blogsData, packagesData, subcategoriesData] = await Promise.all([getBlogs(), getCategory(), getSubCategory()]);

    const blogUrls =
        blogsData?.data
            .map((post: IBlogPost) => ({
                url: `${baseUrl}/news/${post.slug}`,
                lastModified: post.updatedAt ? new Date(post.updatedAt) : new Date(),
                changeFrequency: "weekly" as const,
                priority: 0.8,
            })) ?? [];


    const categoryUrls =
        packagesData?.data?.map((pkg: ICategory) => ({
            url: `${baseUrl}/${pkg.slug}`,
            lastModified: pkg.updatedAt ? new Date(pkg.updatedAt) : new Date(),
            changeFrequency: "monthly" as const,
            priority: 0.7,
        })) ?? [];


    const productUrls =
        subcategoriesData?.data
            ?.flatMap((sub: ISubCategory) =>
                (sub.products ?? []).map((prod: IProduct) => ({
                    url: `${baseUrl}/${sub.category?.slug}/${prod.slug}`,
                    lastModified: prod.updatedAt ? new Date(prod.updatedAt) : new Date(),
                    changeFrequency: "monthly" as const,
                    priority: 0.7,
                })),
            ) ?? [];



    const staticUrls: MetadataRoute.Sitemap = [
        {
            url: `${baseUrl}/`,
            lastModified: new Date(),
            changeFrequency: "weekly",
            priority: 1,
        },
        {
            url: `${baseUrl}/about`,
            lastModified: new Date(),
            changeFrequency: "monthly",
            priority: 0.7,
        },
        {
            url: `${baseUrl}/news`,
            lastModified: new Date(),
            changeFrequency: "monthly",
            priority: 0.7,
        },
        {
            url: `${baseUrl}/contact`,
            lastModified: new Date(),
            changeFrequency: "monthly",
            priority: 0.7,
        },
    ];

    return [...staticUrls, ...blogUrls, ...categoryUrls, ...productUrls];
}