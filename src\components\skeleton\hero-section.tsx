import { Skeleton } from "@/components/ui/skeleton";

export default function HeroSectionSkeleton() {
  return (
    <section className="relative h-[420px] md:h-[400px] flex items-center overflow-hidden">
      <Skeleton className="absolute inset-0 h-full w-full rounded-none" />
      <div className="relative container mx-auto px-2 md:px-4">
        <div className="space-y-3">
          <div className="h-8 md:h-12 w-4/5 md:w-2/3 bg-white/60 rounded" />
          <div className="h-4 w-2/3 md:w-1/2 bg-white/50 rounded" />
        </div>
      </div>
    </section>
  );
}
