import InlineSearchBar from "@/components/search/search-bar";
import { IProduct } from "@/types/category";
import { IApiResponse } from "@/types/response";
import Image from "next/image";
import Link from "next/link";

export const revalidate = 0;

function stripHtml(html: string) {
    return html.replace(/<br\s*\/?>/gi, " ").replace(/<[^>]*>/g, "").trim();
}

function truncate(text: string, n = 140) {
    if (text.length <= n) return text;
    return text.slice(0, n).trim() + "…";
}

export default async function SearchPage(props: {
    searchParams: Promise<{ query?: string; page?: string; field?: string }>;
}) {
    const sp = await props.searchParams;

    const query = (sp.query ?? "").trim();
    const page = Number(sp.page ?? 1);
    const field = (sp.field ?? "name").toLowerCase();
    const limit = 12;

    if (!query) {
        return (
            <div className="container mx-auto px-4 py-12">
                <h1 className="text-2xl md:text-3xl font-semibold mb-2">Search</h1>
                <p className="text-muted-foreground">
                    Type a part / product in the search bar to see results.
                </p>
            </div>
        );
    }

    const url = new URL(`${process.env.NEXT_PUBLIC_API_URL}/product`);
    url.searchParams.set("search", query);
    url.searchParams.set("searchFields", field);
    url.searchParams.set("page", String(page));
    url.searchParams.set("limit", String(limit));

    const res = await fetch(url.toString(), { cache: "no-store" });
    if (!res.ok) {
        return (
            <div className="container mx-auto px-4 py-12">
                <h1 className="text-2xl md:text-3xl font-semibold mb-4">
                    Search Results for “{query}”
                </h1>
                <p className="text-red-600">Failed to load results.</p>
            </div>
        );
    }

    const json = (await res.json()) as IApiResponse<IProduct[]>;
    const products = json.data ?? [];
    const meta = json.meta;

    return (
        <div className="container mx-auto px-4 py-10">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
                <h1 className="text-2xl md:text-3xl font-semibold">
                    Search Results for “{query}”
                </h1>
                <InlineSearchBar defaultQuery={query} />
            </div>

            {products.length === 0 ? (
                <p className="text-muted-foreground">No matches found.</p>
            ) : (
                <ul className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                    {products.map((p) => {
                        const img =
                            (p.image && p.image.length > 0 && p.image[0]) ||
                            "/images/helicopter.png";
                        const desc = truncate(stripHtml(p.description || ""), 160);

                        return (
                            <li key={p.id}>
                                <Link
                                    href={`/${p.subCategory?.category?.slug}/${p.slug}`}
                                    className="group block rounded-2xl border bg-white hover:shadow-lg transition overflow-hidden"
                                >
                                    <div className="relative aspect-[4/3] bg-gray-100">
                                        <Image
                                            src={img}
                                            alt={p.name}
                                            fill
                                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                                            className="object-cover object-center group-hover:scale-105 transition-transform"
                                            priority={false}
                                        />
                                    </div>

                                    <div className="p-4">
                                        <h3 className="text-base font-semibold leading-snug line-clamp-2">
                                            {p.name}
                                        </h3>
                                        <p className="mt-2 text-sm text-muted-foreground line-clamp-3">
                                            {desc || "—"}
                                        </p>
                                    </div>
                                </Link>
                            </li>
                        );
                    })}
                </ul>
            )}

            {meta && meta.lastPage > 1 && (
                <div className="mt-8 flex items-center justify-center gap-3">
                    <PaginationLink
                        disabled={page <= 1}
                        href={`/search?query=${encodeURIComponent(query)}&page=${page - 1}`}
                    >
                        Previous
                    </PaginationLink>
                    <span className="text-sm text-muted-foreground">
                        Page {meta.currentPage} of {meta.lastPage}
                    </span>
                    <PaginationLink
                        disabled={page >= meta.lastPage}
                        href={`/search?query=${encodeURIComponent(query)}&page=${page + 1}`}
                    >
                        Next
                    </PaginationLink>
                </div>
            )}
        </div>
    );
}

function PaginationLink({
    href,
    children,
    disabled,
}: {
    href: string;
    children: React.ReactNode;
    disabled?: boolean;
}) {
    if (disabled)
        return (
            <span className="px-4 py-2 rounded-full border text-sm text-muted-foreground opacity-60 cursor-not-allowed">
                {children}
            </span>
        );
    return (
        <Link
            href={href}
            className="px-4 py-2 rounded-full border text-sm hover:bg-gray-50"
        >
            {children}
        </Link>
    );
}
