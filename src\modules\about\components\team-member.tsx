'use client';

import Image from 'next/image';


// data/team-members.ts
const teamMembers = [
    {
        name: "Mrs. <PERSON><PERSON><PERSON>",
        role: "Chairman",
        photo: "/images/team/1.png",
    },
    {
        name: "Mr. <PERSON>",
        role: "Managing Director / Accountable Manager",
        photo: "/images/team/2.png",
    },
    {
        name: "Mr. <PERSON>",
        role: "Quality Assurance Manager",
        photo: "/images/team/3.png",
    },
    {
        name: "Mr. <PERSON>",
        role: "Material Inspector / Store-Incharge",
        photo: "/images/team/4.png",
    },
    {
        name: "<PERSON><PERSON><PERSON><PERSON>",
        role: "Finance Officer",
        photo: "/images/team/3.png",
    },
    {
        name: "<PERSON><PERSON><PERSON>",
        role: "Receptionist",
        photo: "/images/team/4.png",
    },
];

export default function TeamSection() {
    return (
        <section className="bg-white py-12">
            <div className="container mx-auto px-4">
                <h2 className="text-3xl md:text-4xl font-extrabold text-secondary mb-10 text-center">
                    OUR TEAM
                </h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-10 place-items-center">
                    {teamMembers.map((member) => (
                        <div key={member.name} className="flex flex-col items-center">
                            <div
                                className="w-40 h-40 rounded-full border-4 border-secondary flex items-center justify-center mb-4 overflow-hidden transition-transform duration-300 ease-out hover:scale-115 hover:shadow-lg hover:border-primary"
                            >
                                <Image
                                    src={member.photo}
                                    alt={member.name}
                                    width={160}
                                    height={160}
                                    className="object-cover w-full h-full"
                                    priority
                                />
                            </div>
                            <div className="text-center">
                                <p className="font-bold text-lg text-secondary tracking-wide">
                                    {member.name}
                                </p>
                                <p className="text-primary mt-1 text-sm uppercase font-medium">
                                    {member.role}
                                </p>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </section>
    );
}
