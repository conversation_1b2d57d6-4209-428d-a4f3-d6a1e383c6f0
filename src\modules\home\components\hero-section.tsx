'use client'

import { Input } from '@/components/ui/input'
import { IHomeHero } from '@/types/home'
import { Search } from 'lucide-react'
import { useRouter } from 'next/navigation'
import React, { useState } from 'react'

const HeroSection = ({ homeHero }: { homeHero?: IHomeHero }) => {
  const [q, setQ] = useState('')
  const router = useRouter()

  const onSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    const query = q.trim()
    if (!query) return
    router.push(`/search?query=${encodeURIComponent(query)}&field=name`)
  }
  return (
    <section
      className="relative h-[500px] flex items-center bg-cover bg-center"
      style={{
        backgroundImage: `url(${homeHero?.images || '/images/hero.jpg'})`,
      }}
    >
      <div className="absolute inset-0 bg-black/15"></div>

      <div className="relative w-full container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-[70vh]">
          <h2 className="text-white text-6xl font-bold leading-tight mb-20 drop-shadow-md">
            {homeHero?.titles || 'INSTANT PART SEARCH'}
          </h2>

          <form onSubmit={onSubmit} role="search" aria-label="Part search">
            <div className="flex items-center border-b-2 border-white/80 transition">
              <Input
                value={q}
                onChange={(e) => setQ(e.target.value)}
                placeholder="SEARCH BY PART NUMBER / USE * AS A WILDCARD"
                className="flex-1 bg-transparent border-0 rounded-none text-white font-medium placeholder:text-white/80 uppercase tracking-wide focus:ring-0 focus:outline-none"
                style={{ boxShadow: 'none' }}
                spellCheck={false}
              />
              <button
                type="submit"
                aria-label="Search"
                className="p-2 ml-2 text-primary hover:text-white transition"
              >
                <Search className="w-7 h-7" />
              </button>
            </div>
          </form>
        </div>
      </div>
    </section>
  )
}

export default HeroSection
