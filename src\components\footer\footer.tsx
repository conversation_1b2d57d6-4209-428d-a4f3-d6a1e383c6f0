import { ICategory } from "@/types/category";
import { Phone, Mail, MapPin } from "lucide-react";
import Link from "next/link";

interface FooterProps {
  categories: ICategory[];
}

const Footer = ({ categories }: FooterProps) => {
  return (
    <footer className="bg-secondary text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="md:col-span-2">
            <div className="flex items-center gap-4 mb-6">
              <div>
                <h3 className="text-xl font-bold">MUSTANG AIRWORKS Pvt. Ltd.</h3>
              </div>
            </div>
            <p className="text-sm opacity-90 leading-relaxed mb-6">
              Mustang Airworks pvt Ltd is an ISO 9001:2015 approved helicopter and airplane  parts support organisation staffed by industry professionals with long international experience in helicopter & airplane component sales, maintenance and logistics support. We provide a competitive and efficient service worldwide, covering parts supply on an outright sale or exchange basis as well as overhaul and repair management.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="font-bold mb-4">Quick Links</h4>
            <nav className="space-y-2">
              {categories.map((cat) => (
                <Link key={cat.id} href={cat.slug} className="block text-sm opacity-90 hover:opacity-100 transition-smooth">
                  {cat.name}
                </Link>
              ))}
              <Link href="/about" className="block text-sm opacity-90 hover:opacity-100 transition-smooth">
                About Us
              </Link>
              <Link href="/news" className="block text-sm opacity-90 hover:opacity-100 transition-smooth">
                News & Views
              </Link>
              <Link href="/contact" className="block text-sm opacity-90 hover:opacity-100 transition-smooth">
                Contact Us
              </Link>
            </nav>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="font-bold mb-4">Contact Us</h4>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Phone className="h-4 w-4 opacity-90" />
                <span className="text-sm opacity-90">+977-9801000016</span>
              </div>
              <div className="flex items-center gap-3">
                <Mail className="h-4 w-4 opacity-90" />
                <span className="text-sm opacity-90"><EMAIL></span>
              </div>
              <div className="flex items-start gap-3">
                <MapPin className="h-4 w-4 opacity-90 mt-0.5" />
                <span className="text-sm opacity-90">
                  Neel Sarswati Marg, Lazimpat, Kathmandu, Nepal
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-primary-foreground/20 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-sm opacity-90">
              © 2024 Mustang Airworks Pvt. Ltd. All rights reserved.
            </p>
            <div className="flex gap-6">
              <Link href="/privacy-policy" className="text-sm opacity-90 hover:opacity-100 transition-smooth">Privacy Policy</Link>
              <Link href="/terms" className="text-sm opacity-90 hover:opacity-100 transition-smooth">Terms of Service</Link>
              <Link href="/quality-certification" className="text-sm opacity-90 hover:opacity-100 transition-smooth">Quality Certifications</Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;