import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Plane, Home } from "lucide-react"

export default function NotFound() {
    return (
        <div className="min-h-[600px] bg-gradient-to-b from-blue-50 to-white flex items-center justify-center ">
            <div className="max-w-2xl mx-auto text-center">
                <Card className="border-0 shadow-2xl bg-white/80 backdrop-blur-sm">
                    <CardContent className="p-12">
                        <div className="relative mb-8">
                            <div className="text-8xl font-bold text-secondary select-none">404</div>
                            <div className="absolute inset-0 flex items-center justify-center">
                                <Plane className="w-16 h-16 text-secondary animate-pulse" />
                            </div>
                        </div>

                        <h1 className="text-4xl font-bold text-gray-900 mb-4">Page Not Found</h1>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                            <Button asChild size="lg" className="bg-secondary hover:bg-primary text-white px-8">
                                <Link href="/">
                                    <Home className="w-5 h-5 mr-2" />
                                    Return to Home
                                </Link>
                            </Button>

                            {/* <Button
                                asChild
                                variant="outline"
                                size="lg"
                                className="border-secondary text-secondary hover:bg-blue-50 px-8 bg-transparent"
                            >
                                <Link href="/search">
                                    <Search className="w-5 h-5 mr-2" />
                                    Search Parts
                                </Link>
                            </Button> */}
                        </div>

                        <div className="mt-3 pt-8 border-t border-gray-200">
                            <p className="text-sm text-gray-500 mb-4">Need assistance finding aircraft parts or support?</p>
                            <div className="flex flex-wrap justify-center gap-6 text-sm">
                                <Link href="/contact" className="text-secondary hover:text-blue-800 transition-colors">
                                    Contact Support
                                </Link>
                                <Link href="/about" className="text-secondary hover:text-blue-800 transition-colors">
                                    About Us
                                </Link>
                                <Link href="/aircraft" className="text-secondary hover:text-blue-800 transition-colors">
                                    Aircraft
                                </Link>
                            </div>
                        </div>
                    </CardContent>
                </Card>               
            </div>
        </div>
    )
}
