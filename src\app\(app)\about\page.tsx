import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Plane, Shield, Globe, Clock, CheckCircle, Users, Award, MapPin } from "lucide-react"

export default function AboutPage() {
    return (
        <div className="min-h-screen bg-background">
            {/* Hero Section */}
            {/* <section className="relative bg-primary text-primary-foreground py-20 px-4">
                <div className="container mx-auto px-4 text-center">
                    <div className="flex items-center justify-center gap-3 mb-6">
                        <Plane className="h-12 w-12" />
                        <h1 className="text-4xl md:text-6xl font-bold text-balance">Mustang Airworks</h1>
                    </div>
                    <p className="text-xl md:text-2xl text-primary-foreground/90 mb-8 text-balance">
                        Elevating Aviation Parts Suppliers in Nepal & South Asia
                    </p>
                    <Badge variant="secondary" className="text-lg px-4 py-2">
                        CAAN Approved NCAR D3.3 Supplier Organization
                    </Badge>
                </div>
            </section> */}

            {/* Company Overview */}
            <section className="py-16 px-4">
                <div className="container mx-auto px-4">
                    <div className="grid md:grid-cols-2 gap-12 items-center">
                        <div>
                            <h2 className="text-3xl font-bold mb-6 text-balance">About Mustang Airworks</h2>
                            <p className="text-lg leading-relaxed mb-6">
                                Mustang Airworks Pvt. Ltd. stands as an aviation spare parts supplier partner with headquarters in
                                Kathmandu, Nepal. Operating under the stringent Civil Aviation Authority of Nepal (CAAN) NCAR D3.3
                                Suppliers Organization approval, we specialize in the end-to-end supply chain for aviation assets.
                            </p>
                            <p className="text-lg leading-relaxed">
                                We bridge the gap between global supply chains and regional aviation operators, utilizing deep technical
                                expertise and robust international sourcing networks to deliver reliable, traceable, and airworthy
                                components with speed and precision.
                            </p>
                        </div>
                        <Card className="bg-primary/10">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <MapPin className="h-5 w-5 text-primary" />
                                    Company Details
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <div className="flex justify-between">
                                    <span className="font-medium">Established:</span>
                                    <span>2024</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="font-medium">Location:</span>
                                    <span>Kathmandu, Nepal</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="font-medium">PAN/VAT:</span>
                                    <span>*********</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="font-medium">Regulatory Status:</span>
                                    <Badge variant="outline">CAAN Approved</Badge>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </section>

            {/* Leadership */}
            <section className="py-16 px-4 bg-secondary/15">
                <div className="container mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-12 text-balance">Leadership</h2>
                    <Card className="max-w-4xl mx-auto">
                        <CardHeader className="text-center">
                            <div className="w-24 h-24 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                                <Users className="h-12 w-12 text-primary-foreground" />
                            </div>
                            <CardTitle className="text-2xl">Mr. Ishwor Lamichhane (Ronan)</CardTitle>
                            <CardDescription className="text-lg">Managing Director & Accountable Manager</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <p className="text-center leading-relaxed">
                                Mr. Lamichhane brings over a decade of distinguished experience across diverse aviation sectors,
                                including airline operations, aviation finance, charter management, and international parts logistics. A
                                proven entrepreneur, he is the driving force behind Mustang Airworks and has successfully co-owned
                                Mustang Helicopter Pvt. Ltd. and Heli Air Nepal Pvt. Ltd.
                            </p>
                        </CardContent>
                    </Card>
                </div>
            </section>

            {/* Core Services */}
            <section className="py-16 px-4">
                <div className="container mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-12 text-balance">Core Services & Solutions</h2>
                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {[
                            {
                                icon: <Shield className="h-8 w-8" />,
                                title: "Certified Parts Supply",
                                description:
                                    "Sourcing and supply of CAAN, EASA, and FAA-certified spare parts for diverse aircraft platforms.",
                            },
                            {
                                icon: <Plane className="h-8 w-8" />,
                                title: "Aircraft Fluids & Lubricants",
                                description: "Supply of approved engine oils, hydraulic fluids, greases, and specialty lubricants.",
                            },
                            {
                                icon: <Globe className="h-8 w-8" />,
                                title: "Avionics & Electrical Components",
                                description: "Provision of critical avionics systems and electrical parts.",
                            },
                            {
                                icon: <CheckCircle className="h-8 w-8" />,
                                title: "Material Management",
                                description: "Expert inspection, certified storage solutions, and quality control assurance processes.",
                            },
                            {
                                icon: <Award className="h-8 w-8" />,
                                title: "Documentation & Certification",
                                description:
                                    "Complete provision of essential documentation including Certificates of Conformity and various airworthiness tags.",
                            },
                            {
                                icon: <Clock className="h-8 w-8" />,
                                title: "AOG & Emergency Support",
                                description:
                                    "Rapid response coordination for Aircraft on Ground situations, including urgent parts sourcing and logistics.",
                            },
                        ].map((service, index) => (
                            <Card key={index} className="hover:shadow-lg transition-shadow">
                                <CardHeader>
                                    <div className="text-primary mb-2">{service.icon}</div>
                                    <CardTitle className="text-xl">{service.title}</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="leading-relaxed">{service.description}</p>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </div>
            </section>

            {/* Why Partner With Us */}
            <section className="py-16 px-4 bg-secondary/15">
                <div className="container mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-12 text-balance">Why Partner with Mustang Airworks?</h2>
                    <div className="grid md:grid-cols-2 gap-8">
                        {[
                            "Regulatory Credibility: CAAN NCAR D3.3 Approval ensures adherence to Nepal's highest aviation standards",
                            "Assured Quality: EASA-influenced Quality Management System guaranteeing full part traceability and airworthiness",
                            "Expert Leadership: Management team with proven industry success and deep sector knowledge",
                            "Global Network: Strong alliances with international OEMs and approved distributors",
                            "Strategic Advantage: Prime location in Kathmandu for swift logistics",
                            "Operational Agility: Rapid response capability for critical requirements, minimizing downtime",
                            "Tailored Solutions: Customizable support packages designed to meet specific fleet and operational needs",
                        ].map((advantage, index) => (
                            <div key={index} className="flex items-start gap-3">
                                <CheckCircle className="h-6 w-6 text-primary flex-shrink-0 mt-1" />
                                <p className="leading-relaxed">{advantage}</p>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Vision & Mission */}
            <section className="py-16 px-4">
                <div className="container mx-auto px-4">
                    <div className="grid md:grid-cols-2 gap-8">
                        <Card className="bg-primary text-primary-foreground">
                            <CardHeader>
                                <CardTitle className="text-2xl flex items-center gap-2">
                                    <Globe className="h-6 w-6" />
                                    Our Vision
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <p className="text-lg leading-relaxed">
                                    &quot;To be Nepal&apos;s undisputed leader in trusted aviation logistics and parts supplier, setting the
                                    industry benchmark for regulatory compliance, operational speed, and unparalleled service excellence.&quot;
                                </p>
                            </CardContent>
                        </Card>
                        <Card className="bg-secondary text-white">
                            <CardHeader>
                                <CardTitle className="text-2xl flex items-center gap-2">
                                    <Shield className="h-6 w-6" />
                                    Our Mission
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <p className="text-lg leading-relaxed">
                                    &quot;To empower aviation operators across Nepal and South Asia with reliable, timely, and fully compliant
                                    parts and logistics solutions. We enable safer and more efficient flight operations through expertise,
                                    robust systems, and unwavering commitment to aviation standards.&quot;
                                </p>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </section>

            <section className="py-16 px-4 bg-secondary/15">
                <div className="container mx-auto px-4 text-center">
                    <h2 className="text-3xl font-bold mb-8 text-balance">Commitment to Compliance & Safety</h2>
                    <p className="text-lg leading-relaxed max-w-4xl mx-auto mb-8">
                        Mustang Airworks operates in strict accordance with Civil Aviation Authority of Nepal (CAAN) regulations,
                        ICAO Annex 19 (Safety Management) standards, and EASA Part-145 practices. Every component is handled with
                        meticulous attention to certification and traceability.
                    </p>
                    <div className="flex flex-wrap justify-center gap-4">
                        <Badge variant="default" className="text-sm px-3 py-1">
                            CAAN Regulations
                        </Badge>
                        <Badge variant="default" className="text-sm px-3 py-1">
                            ICAO Annex 19
                        </Badge>
                        <Badge variant="default" className="text-sm px-3 py-1">
                            EASA Part-145
                        </Badge>
                        <Badge variant="default" className="text-sm px-3 py-1">
                            SMS Certified
                        </Badge>
                        <Badge variant="default" className="text-sm px-3 py-1">
                            QMS Compliant
                        </Badge>
                    </div>
                </div>
            </section>

            {/* Contact Section */}
            <section className="py-16 px-4">
                <div className="container mx-auto px-4 text-center">
                    <h2 className="text-3xl font-bold mb-8 text-balance">Contact Mustang Airworks</h2>
                    <div className="grid md:grid-cols-3 gap-8 mb-8">
                        <div>
                            <MapPin className="h-8 w-8 mx-auto mb-4" />
                            <h3 className="font-semibold mb-2">Address</h3>
                            <p>
                                Neel Sarswati Marg, Lazimpat
                                <br />
                                Kathmandu, Nepal
                            </p>
                        </div>
                        <div>
                            <Globe className="h-8 w-8 mx-auto mb-4" />
                            <h3 className="font-semibold mb-2">Email</h3>
                            <p><EMAIL></p>
                        </div>
                        <div>
                            <Plane className="h-8 w-8 mx-auto mb-4" />
                            <h3 className="font-semibold mb-2">Website</h3>
                            <p>www.mustangairwork.com</p>
                        </div>
                    </div>
                    <Button variant="secondary" size="lg" className="text-white text-lg px-8 py-3">
                        Get In Touch
                    </Button>
                </div>
            </section>
        </div>
    )
}


// import HeroSection from '@/components/hero-section/hero-section'
// import QuickLinksSection from '@/modules/about/components/quick-links'
// import React from 'react'

// const AboutUsPage = () => {
//     return (
//         <div>
//             <HeroSection
//                 image="/images/hero-section.jpg"
//                 title="ABOUT US"
//             />
//             <QuickLinksSection />
//             <div>
//                 <section className="bg-white py-12 border-t border-gray-100">
//                     <div className="container mx-auto px-2 md:px-4">
//                         <h2 className="text-3xl font-bold text-secondary mb-6">
//                             About Mustang Airworks
//                         </h2>
//                         <p className="mb-5 text-gray-700 text-lg">
//                             Established in 2018, <span className="font-semibold text-primary">Mustang Airworks Pvt. Ltd.</span> is dedicated to providing cost-effective, reliable, and swift supply of aviation material and technical support to airlines, helicopter operators, and government sectors across South Asia. Our team brings deep technical expertise and a customer-driven approach to aircraft and helicopter parts management, MRO support, and aviation procurement services.
//                         </p>
//                         <ul className="space-y-3 mb-7">
//                             <li className="flex items-start gap-3">
//                                 <span className="mt-1 w-2.5 h-2.5 rounded-full bg-primary flex-shrink-0" />
//                                 <span>
//                                     <span className="font-semibold text-secondary">400,000+ lines</span> available—covering parts, components, and consumables for all major airframes & rotorcraft platforms.
//                                 </span>
//                             </li>
//                             <li className="flex items-start gap-3">
//                                 <span className="mt-1 w-2.5 h-2.5 rounded-full bg-primary flex-shrink-0" />
//                                 <span>
//                                     <span className="font-semibold text-secondary">Expert MRO, teardown, logistics, and consignment solutions</span> for commercial, regional, and defense aviation operators.
//                                 </span>
//                             </li>
//                             <li className="flex items-start gap-3">
//                                 <span className="mt-1 w-2.5 h-2.5 rounded-full bg-primary flex-shrink-0" />
//                                 <span>
//                                     <span className="font-semibold text-secondary">In-house technical team</span> for aircraft/engine teardown, legacy fleet support, and project management.
//                                 </span>
//                             </li>
//                             <li className="flex items-start gap-3">
//                                 <span className="mt-1 w-2.5 h-2.5 rounded-full bg-primary flex-shrink-0" />
//                                 <span>
//                                     Fully compliant with <span className="font-semibold text-secondary">global aviation standards</span> and end-of-life part management.
//                                 </span>
//                             </li>
//                         </ul>
//                         <p className="text-gray-700">
//                             Mustang Airworks is committed to keeping the region’s aircraft and helicopters <span className="font-semibold text-primary">airworthy, mission-ready, and cost-effective—anywhere, anytime.</span>
//                         </p>
//                     </div>
//                 </section>
//             </div>

//         </div>
//     )
// }

// export default AboutUsPage