'use client';

import { ChevronRight } from 'lucide-react';
import Link from 'next/link';

const productLines = [
  [
    { label: 'PITOT-STATIC TEST EQUIPMENT', href: '/products/pitot-static' },
    { label: 'A109E GENERATORS & GENERATOR CONTROL UNITS', href: '/products/a109e-generators' },
    { label: 'A109E/S ELECTRONIC DISPLAY UNITS (EDU) & DATA ACQUISITION UNITS (DAU)', href: '/products/a109e-electronics' },
    { label: 'A109E MAIN ROTOR BLADES', href: '/products/a109e-main-rotor-blades' },
    { label: 'A109E/S MAIN ROTOR DAMPERS & SERVO ACTUATORS', href: '/products/a109e-rotor-dampers' },
    { label: 'A320, B737NG, B777 ICE DETECTORS & PITOT PROBES', href: '/products/ice-detectors' },
    { label: 'A320 & B737NG COFFEE MAKERS & WATER BOILERS', href: '/products/coffee-makers' },
    { label: 'A320 & B737NG WATER HEATERS', href: '/products/water-heaters' },
    { label: 'A320 & E190 FIRE CONTROL', href: '/products/fire-control' },
    { label: 'A320 OUTFLOW VALVES', href: '/products/outflow-valves' },
    { label: 'HEADSETS & FUEL TESTING', href: '/products/headsets-fuel-testing' },
    { label: 'AIRBUS FAMILY VHF, GPS, SATCOM ANTENNAS', href: '/products/airbus-antennas' }
  ],
  [
    { label: 'B737NG & B787 SMOKE HOODS', href: '/products/b737ng-b787-smokehoods' },
    { label: 'B737NG & CLASSIC BALLSCREWS', href: '/products/b737ng-ballscrews' },
    { label: 'CASA C295 STARTER GENERATOR', href: '/products/casa-c295-starter-generator' },
    { label: 'CFM56-7 ENGINE HARNESSES', href: '/products/cfm56-engine-harness' },
    { label: 'EFI890R GLASS COCKPIT DISPLAYS', href: '/products/efi890r-glass-displays' },
    { label: 'EMBRAER E190/E170 SILL HEATERS, DRAIN MASTS & HIGH STAGE BLEED VALVES', href: '/products/e190-sill-heaters' },
    { label: 'PPG WINDSHIELDS & ASSOCIATED PRODUCTS', href: '/products/ppg-windshields' },
    { label: 'EMERGENCY LOCATOR TRANSMITTERS (ELTs)', href: '/products/elts' },
    { label: 'HAWKER 700 NOSE TO TAIL SUPPORT', href: '/products/hawker-700-support' },
    { label: 'HONEYWELL PRIMUS WU-880 WEATHER RADAR', href: '/products/primus-weather-radar' },
    { label: 'TCAS II CHANGE 7.1 UPGRADE', href: '/products/tcas-upgrade' }
  ]
];

export default function ProductLinesSection() {
  return (
    <section className="bg-white py-8 border-t border-gray-100">
      <div className="container mx-auto px-4">
        <div className="mb-4">
          <p className="flex items-start text-gray-500 text-base text-center mb-6">
            Browse our product lines below, or call us at <a href="tel:+977-1-4169802" className="text-primary font-semibold hover:underline">+977-1-4169802</a> to speak to a sales adviser
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center justify-center">
          {productLines.map((column, idx) => (
            <ul key={idx} className="space-y-2">
              {column.map((item) => (
                <li key={item.label}>
                  <Link
                    href={item.href}
                    className="group flex items-center gap-2 text-xl text-secondary font-semibold hover:text-primary transition"
                  >
                    <ChevronRight className="w-5 h-5 transform transition-transform duration-200 group-hover:translate-x-1" />
                    <span>{item.label}</span>
                  </Link>
                </li>
              ))}
            </ul>
          ))}
        </div>
        <div className="mt-8 flex justify-center">
          <div className="bg-gradient-to-r from-secondary to-primary px-6 py-4 rounded shadow text-white font-bold text-lg">
            LOOKING FOR MILITARY PARTS? Contact us for tailored support!
          </div>
        </div>
      </div>
    </section>
  );
}
