import Link from "next/link";
import { notFound } from "next/navigation";
import type { Metadata } from "next";

import getCategory from "@/actions/category/get-category";
import getSubCategory from "@/actions/subcategory/get-subcategory";
import getProduct from "@/actions/product/get-product";
import getProductById from "@/actions/product/get-product-by-id";

import type { ICategory, ISubCategory, IProduct } from "@/types/category";
import ProductTabsSection from "@/modules/product/component/product-tab-section";

export const revalidate = 0;

type Params = { category: string; product: string };

const FALLBACK = "/images/helicopter.png";

function normalizeImage(src?: string | string[] | null): string {
    if (Array.isArray(src)) {
        const first = src.find((s) => typeof s === "string" && s.trim().length > 0);
        return first ?? FALLBACK;
    }
    if (typeof src === "string" && src.trim().length > 0) {
        return src;
    }
    return FALLBACK;
}

async function getAllProducts(perPage = 20) {
    let allProducts: IProduct[] = [];
    let page = 1;
    let lastPage = 1;

    do {
        const res = await getProduct(page, perPage);
        allProducts = allProducts.concat(res.data);
        lastPage = res.meta.lastPage;
        page++;
    } while (page <= lastPage);

    return allProducts;
}


export async function generateMetadata(
    { params }: { params: Promise<Params> }
): Promise<Metadata> {
    const { category, product } = await params;

    const [catRes, subRes, prodRes] = await Promise.all([
        getCategory(),
        getSubCategory(),
        getProduct(),
    ]);

    const categories: ICategory[] = catRes?.data ?? [];
    const subcategories: ISubCategory[] = subRes?.data ?? [];
    const products: IProduct[] = prodRes?.data ?? [];

    const currentCategory = categories.find((c) => c.slug === category);
    if (!currentCategory) return { title: "Product | Mustang Airworks" };

    const prod = products.find((p) => p.slug === product);
    if (!prod) return { title: `${currentCategory.name} | Mustang Airworks` };

    const sub = subcategories.find((s) => s.id === prod.subCategoryId);
    if (!sub || sub.categoryId !== currentCategory.id) {
        return { title: `${prod.name} | Mustang Airworks` };
    }

    const title = `${prod.name} | ${currentCategory.name} | Mustang Airworks`;
    const description =
        typeof prod?.name === "string"
            ? `Details, programs and support for ${prod.name}.`
            : `Explore ${currentCategory.name} product.`;

    return { title, description, openGraph: { title, description } };
}

export async function generateStaticParams() {
    const subRes = await getSubCategory();
    const categories = (await getCategory())?.data ?? [];
    const categoriesById = new Map(categories.map(c => [c.id, c]));

    const subcategories = subRes?.data ?? [];

    const params = [];

    for (const sub of subcategories) {
        const category = categoriesById.get(sub.categoryId);
        if (!category) continue;

        for (const product of sub.products ?? []) {
            if (!product.slug) continue;

            params.push({
                category: category.slug,
                product: product.slug,
            });
        }
    }
    return params;
}


export default async function ProductPage(
    { params }: { params: Promise<Params> }
) {
    const { category: categorySlug, product: productSlug } = await params;

    const [catRes, subRes] = await Promise.all([
        getCategory(),
        getSubCategory(),
        getProduct(),
    ]);

    const categories: ICategory[] = catRes?.data ?? [];
    const subcategories: ISubCategory[] = subRes?.data ?? [];
    // const products: IProduct[] = prodRes?.data ?? [];

    const category = categories.find((c) => c.slug === categorySlug);
    if (!category) {
        console.error(`Category not found for slug: ${categorySlug}`);
        notFound();
    }

    const products = await getAllProducts();
    const productBasic = products.find(p => p.slug === productSlug);
    if (!productBasic) notFound();


    const sub = subcategories.find((s) => s.id === productBasic.subCategoryId);
    if (!sub || sub.categoryId !== category.id) notFound();

    const productRes = await getProductById(productBasic.id);
    const product = productRes.data;
    // if (!product) notFound();

    const heroImage = normalizeImage(product.image);

    console.log("test", { category, productBasic, sub, productRes });


    if (!sub || sub.categoryId !== category.id) {
        console.error(`Invalid subcategory or category mismatch for product ${productSlug}`);
        notFound();
    }

    return (
        <section className="bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
            {/* 
            <div className="container mx-auto px-4 py-8 hidden md:grid md:grid-cols-2 gap-0">
                <div className="relative w-full max-w-full">
                    <div className="relative w-full aspect-[16/9]">
                        {(heroImage) && (
                            <Image
                                src={heroImage}
                                alt={product.name}
                                fill
                                className="object-cover"
                                sizes="(max-width: 768px) 100vw, 50vw"
                                priority
                            />
                        )}
                    </div>

                    <h1 className="absolute bottom-11 left-4 text-black text-3xl md:text-4xl font-bold drop-shadow-lg">
                        {product.name}
                    </h1>

                    <div className="bg-gradient-to-r from-secondary to-primary text-white p-2">
                        <p
                            className="prose max-w-none"
                            dangerouslySetInnerHTML={{ __html: (product as IProduct).description ?? "" }}
                        />
                    </div>
                </div>

                <div className="flex justify-center md:justify-end">
                    <div className="relative w-full aspect-[16/9]">
                        {(heroImage) && (
                            <Image
                                src={heroImage}
                                alt={product.name}
                                fill
                                className="object-cover"
                                sizes="(max-width: 768px) 100vw, 50vw"
                            />
                        )}
                    </div>
                </div>

            </div> */}


            <div className="relative h-72 md:h-[550px] overflow-hidden">
                <div
                    className="absolute inset-0 bg-cover bg-center"
                    style={{ backgroundImage: `url(${heroImage})` }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent" />
                <div className="container mx-auto h-full px-4 relative z-10 flex items-end pb-6">
                    <div>
                        <nav className="text-sm text-white/80 mb-2">
                            <Link href="/" className="hover:text-white">Home</Link>
                            <span className="mx-2">/</span>
                            <Link href={`/${category.slug}`} className="hover:text-white">{category.name}</Link>
                            <span className="mx-2">/</span>
                            <span className="text-white">{product.name}</span>
                        </nav>
                        <h1 className="text-3xl md:text-4xl font-bold text-white drop-shadow">{product.name}</h1>
                    </div>
                </div>

                {/* <div className="bg-gradient-to-r from-secondary to-primary text-white p-2">
                    <p
                        className="prose max-w-none"
                        dangerouslySetInnerHTML={{ __html: (product as IProduct).description ?? "" }}
                    />
                </div> */}
            </div>

            <div className="">
                <div className="container mx-auto px-4">
                    {product?.slug ? (
                        <article className="prose max-w-none prose-headings:text-secondary prose-a:text-primary text-black  py-6">
                            {"description" in product ? (
                                <div
                                    dangerouslySetInnerHTML={{ __html: (product as IProduct).description ?? "" }}
                                />
                            ) : (
                                <p>No description available.</p>
                            )}
                        </article>
                    ) : null}
                </div>
            </div>

            <div className="container mx-auto px-4  grid grid-cols-1 gap-8 ">
                <div>
                    <ProductTabsSection tabs={product.tabs} />
                </div>
            </div>
        </section>
    );
}
