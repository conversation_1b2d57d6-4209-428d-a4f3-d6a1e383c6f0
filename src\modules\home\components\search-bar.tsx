"use client";

import type React from "react";
import { useState, useRef } from "react";
import { useRouter } from "next/navigation";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

export function SearchBar() {
  const [query, setQuery] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);
  const router = useRouter(); 

  const handleSearch = (e?: React.FormEvent) => {
    e?.preventDefault();
    if (!query.trim()) return;

    router.push(`/search?query=${encodeURIComponent(query.trim())}`);

    inputRef.current?.blur();
  };

  return (
    <div className="bg-gradient-to-r from-secondary/50 to-primary/50">
      <form
        onSubmit={handleSearch}
        className="w-full container mx-auto px-4 py-8"
        role="search"
        aria-label="Part search"
        autoComplete="off"
      >
        <div className="relative w-full">
          <Search
            className="absolute left-4 top-1/2 -translate-y-1/2 text-muted-foreground h-5 w-5 pointer-events-none"
            aria-hidden="true"
          />

          <Input
            ref={inputRef}
            type="text"
            placeholder="SEARCH BY PART NUMBER / USE * AS A WILDCARD"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="w-full pl-12 pr-24 py-7 rounded-full border bg-white border-secondary focus:ring-2 focus:ring-secondary focus:border-secondary transition"
            aria-label="Search by part number"
            spellCheck={false}
          />

          <Button
            type="submit"
            disabled={!query.trim()}
            className="absolute right-2 top-1/2 -translate-y-1/2 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-full px-5 py-2 disabled:opacity-50 disabled:cursor-not-allowed transition"
            aria-disabled={!query.trim()}
          >
            Search
          </Button>
        </div>
      </form>
    </div>
  );
}
