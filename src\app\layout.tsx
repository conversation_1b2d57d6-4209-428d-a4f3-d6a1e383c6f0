import type { Metada<PERSON> } from "next";
import { Poppins } from "next/font/google";
import "./globals.css";
import NavbarLoader from "@/components/navbar/navbar-loader";
import FooterSection from "@/components/footer/footer-section";

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
});

export const metadata: Metadata = {
  title: "Mustang Airworks",
  description: "Aerospace Parts Supplier",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${poppins.variable} ${poppins.className} antialiased`}      >
        <NavbarLoader />
        {children}
        <FooterSection />
      </body>
    </html>
  );
}
