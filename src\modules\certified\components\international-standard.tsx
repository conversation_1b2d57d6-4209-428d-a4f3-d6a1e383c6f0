import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Globe, Plane, Shield } from 'lucide-react'
import React from 'react'

const InternationalStandard = () => {
    return (
        <div>
            <section className="w-full py-16">
                <div className="container mx-auto px-4">
                    <div className="text-center space-y-4 mb-12">
                        <Badge className="bg-primary/10 text-secondary">International Standards</Badge>
                        <h2 className="text-4xl text-center font-bold bg-gradient-to-r from-secondary to-primary bg-clip-text text-transparent mb-2">
                            Global Aviation Compliance
                        </h2>
                        <p className="mx-auto max-w-3xl text-gray-600 md:text-xl">
                            We maintain compliance with international aviation standards and work with global regulatory bodies
                        </p>
                    </div>
                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                        <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                            <CardHeader className="text-center">
                                <Globe className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                                <CardTitle>EASA Standards</CardTitle>
                                <CardDescription>European Aviation Safety Agency</CardDescription>
                            </CardHeader>
                            <CardContent className="text-center">
                                <p className="text-sm text-gray-600">
                                    EASA Part-145 practices and EASA-influenced quality management systems
                                </p>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                            <CardHeader className="text-center">
                                <Shield className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                                <CardTitle>FAA Compliance</CardTitle>
                                <CardDescription>Federal Aviation Administration</CardDescription>
                            </CardHeader>
                            <CardContent className="text-center">
                                <p className="text-sm text-gray-600">FAA-certified parts handling and documentation standards</p>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                            <CardHeader className="text-center">
                                <Plane className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                                <CardTitle>ICAO Standards</CardTitle>
                                <CardDescription>International Civil Aviation Organization</CardDescription>
                            </CardHeader>
                            <CardContent className="text-center">
                                <p className="text-sm text-gray-600">
                                    ICAO Annex 19 Safety Management and international best practices
                                </p>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </section>
        </div>
    )
}

export default InternationalStandard