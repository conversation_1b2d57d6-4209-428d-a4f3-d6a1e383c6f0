import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Building, Facebook, Instagram, Linkedin, Mail, MapPin, Phone, Twitter } from 'lucide-react'
import Link from 'next/link'
import React from 'react'

const ContactInformation = () => {
    return (
        <div className="space-y-6">
            <Card className="border-0 shadow-lg">
                <CardHeader className="pb-4">
                    <CardTitle className="text-2xl">Contact Information</CardTitle>
                    <CardDescription>
                        Reach out to us through any of these channels.
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                    <div className="flex items-start space-x-4">
                        <div className="bg-secondary/10 p-3 rounded-full">
                            <Mail className="h-5 w-5 text-secondary" />
                        </div>
                        <div>
                            <h3 className="font-medium">Email</h3>
                            <Link
                                target="_blank"
                                href="mailto:<EMAIL>"
                                className="text-secondary hover:underline"
                            >
                                <EMAIL>
                            </Link>
                        </div>
                    </div>

                    <div className="flex items-start space-x-4">
                        <div className="bg-secondary/10 p-3 rounded-full">
                            <Phone className="h-5 w-5 text-secondary" />
                        </div>
                        <div>
                            <h3 className="font-medium">Phone</h3>
                            <Link
                                href={"tel:+977-9801000016"}
                                className="text-secondary hover:underline"
                            >
                                +977-9801000016
                            </Link>
                        </div>
                    </div>

                    <div className="flex items-start space-x-4">
                        <div className="bg-secondary/10 p-3 rounded-full">
                            <MapPin className="h-5 w-5 text-secondary" />
                        </div>
                        <div>
                            <h3 className="font-medium">Office Location</h3>
                            <Link
                                target="_blank"
                                href={"https://maps.app.goo.gl/nQNZbSaeuUEqKpPRA"}
                                className="text-secondary hover:underline"
                            >
                                Kathmandu, Nepal
                            </Link>
                        </div>
                    </div>

                    <div className="flex items-start space-x-4">
                        <div className="bg-secondary/10 p-3 rounded-full">
                            <Building className="h-5 w-5 text-secondary" />
                        </div>
                        <div>
                            <h3 className="font-medium">Office Hours</h3>
                            <p className="mt-2 text-[#444] text-sm font-medium">
                                <span className="font-bold">Sunday - Thursday:</span> 10 AM - 5 PM
                            </p>
                            <p className="mt-1 text-[#444] text-sm font-medium">
                               <span className="font-bold">Friday:</span>  10 AM - 4:30 PM
                            </p>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <Card className="border-0 shadow-lg">
                <CardHeader className="pb-4">
                    <CardTitle className="text-2xl">Connect With Us</CardTitle>
                    <CardDescription>
                        Follow us on social media for recent updates.
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="flex space-x-4 justify-center">
                        <Link
                            href="#"
                            target="_blank"
                            className="bg-secondary/10 p-3 rounded-full hover:bg-secondary/20 transition-colors"
                        >
                            <Facebook className="h-6 w-6 text-secondary" />
                            <span className="sr-only">Facebook</span>
                        </Link>
                        <Link
                            href="#"
                            target="_blank"
                            className="bg-secondary/10 p-3 rounded-full hover:bg-secondary/20 transition-colors"
                        >
                            <Twitter className="h-6 w-6 text-secondary" />
                            <span className="sr-only">Twitter</span>
                        </Link>
                        <Link
                            href="#"
                            target="_blank"
                            className="bg-secondary/10 p-3 rounded-full hover:bg-secondary/20 transition-colors"
                        >
                            <Instagram className="h-6 w-6 text-secondary" />
                            <span className="sr-only">Instagram</span>
                        </Link>
                        <Link
                            href="#"
                            target="_blank"
                            className="bg-secondary/10 p-3 rounded-full hover:bg-secondary/20 transition-colors"
                        >
                            <Linkedin className="h-6 w-6 text-secondary" />
                            <span className="sr-only">LinkedIn</span>
                        </Link>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}

export default ContactInformation