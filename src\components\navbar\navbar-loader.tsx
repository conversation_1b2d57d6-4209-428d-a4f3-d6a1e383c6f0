import getCategory from "@/actions/category/get-category";
import getProduct from "@/actions/product/get-product";
import getSubCategory from "@/actions/subcategory/get-subcategory";
import { IProduct, ISubCategory } from "@/types/category";
import NavbarSkeleton from "../skeleton/navbar-skeleton";
import dynamic from "next/dynamic";
import getPages from "@/actions/pages/get-pages";

const NavbarClient = dynamic(() => import("./navbar"), {
    ssr: true,
    loading: () => <NavbarSkeleton />,
});

export interface NavItem {
    id: string;
    name: string;
    href: string
}
export interface NavCategory {
    id: string;
    category: string;
    items: NavItem[]
}
export type NavLink = {
    id?: string;
    label: string;
    href: string;
    subItems?: NavItem[];
    subCategories?: NavCategory[];
    dropdownPages?: {
        id: string;
        label: string;
        href: string;
    }[];
};

export default async function NavbarLoader() {
    const [catRes, subRes, prodRes, pagesRes] = await Promise.all([
        getCategory(),
        getSubCategory(),
        getProduct(),
        getPages(),

    ]);

    const categories = catRes?.data ?? [];
    const subcategories = subRes?.data ?? [];
    const products = prodRes?.data ?? [];
    const pages = pagesRes?.data ?? [];

    const visibleCategories = categories.filter(cat => cat.showOnNav);

    const subsByCategory: Record<string, ISubCategory[]> = {};
    for (const s of subcategories) {
        if (!s?.categoryId || !s.showOnNav) continue;
        (subsByCategory[s.categoryId] ??= []).push(s);
    }
    const prodsBySub: Record<string, IProduct[]> = {};
    for (const p of products) {
        if (!p?.subCategoryId || !p.showOnNav) continue;
        (prodsBySub[p.subCategoryId] ??= []).push(p);
    }

    const subcategoriesWithProducts = subRes?.data ?? [];

    const dynamicCategoryLinks: NavLink[] = visibleCategories.map(cat => {
        const subs = subcategoriesWithProducts.filter(sub => sub.categoryId === cat.id && sub.showOnNav);

        const subCategories: NavCategory[] = subs
            .map(sub => {
                const prods = (sub.products ?? []).filter(p => p.showOnNav && p.name && p.slug);
                const items: NavItem[] = prods.map(p => ({
                    id: p.id,
                    name: p.name,
                    href: `/${cat.slug}/${p.slug}`,
                }));
                if (items.length === 0) return null;
                return {
                    id: sub.id,
                    category: sub.name,
                    items
                };
            })
            .filter(Boolean) as NavCategory[];

        return {
            id: cat.id,
            label: cat.name.toUpperCase(),
            href: `/${cat.slug}`,
            subCategories: subCategories.length ? subCategories : undefined,
        };
    });

    const aboutUsCategory = categories.find(cat => cat.slug === "about-us");

    const pagesForAboutUs = pages.filter(
        page => page.categoryId === aboutUsCategory?.id && page.showOnNav
    );

    const navLinks: NavLink[] = [
        { label: "HOME", href: "/" },
        ...dynamicCategoryLinks,
        // { label: "ABOUT US", href: "/about" },
        {
            label: "ABOUT US",
            href: "/about",
            dropdownPages: pagesForAboutUs.map(page => ({
                id: page.id,
                label: page.title,
                href: `/about/${page.slug}`,
            })),
        },
        { label: "NEWS & VIEWS", href: "/news" },
        { label: "CONTACT US", href: "/contact" },
    ];

    return <NavbarClient navLinks={navLinks} />;
}
