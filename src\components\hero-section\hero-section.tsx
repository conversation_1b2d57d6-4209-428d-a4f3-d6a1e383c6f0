'use client';

import Image from 'next/image';
import React from 'react';

interface HeroSectionProps {
    image: string;
    title: string;
}

const HeroSection: React.FC<HeroSectionProps> = ({
    image,
    title,
}) => {
    return (
        <section className="relative h-[420px] md:h-[400px] flex items-center">
            {/* Full background image */}
            <div className="absolute inset-0">
                <Image
                    src={image}
                    alt={title}
                    fill  
                    className="object-cover object-center"
                    priority
                />
            </div>
            <div className="container mx-auto px-2 md:px-4">
                <h1 className="text-white text-4xl md:text-5xl font-extrabold mb-4 drop-shadow-md">
                    {title}
                </h1>
            </div>
        </section>
    );
};

export default HeroSection;
