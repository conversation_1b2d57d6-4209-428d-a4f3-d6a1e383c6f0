import { IProduct } from "@/types/category";
import { IApiResponse } from "@/types/response";

export const revalidate = 0;

type Meta = { total: number; items: number; currentPage: number; perPage: number; lastPage: number };
type Resp = IApiResponse<IProduct[]> & { meta: Meta };

export default async function getProduct(page = 1, perPage = 12) {
  const url = `${process.env.NEXT_PUBLIC_API_URL}/product?page=${page}&perPage=${perPage}`;
  const res = await fetch(url, { cache: "no-store" });
  const data: Resp = await res.json();
  return data; 
}
