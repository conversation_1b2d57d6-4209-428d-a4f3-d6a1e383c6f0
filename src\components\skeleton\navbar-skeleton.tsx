import { Skeleton } from "@/components/ui/skeleton";

export default function NavbarSkeleton() {
  return (
    <div className="bg-gray-300">
      <header className="bg-gray-40 border-b border-gray-200">
        <div className="flex flex-wrap items-center justify-center gap-4 px-2 md:px-4 py-3 md:py-10 container mx-auto">
          <div className="flex md:hidden items-center gap-4 text-sm">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-40" />
          </div>

          <div className="flex items-center gap-3 flex-shrink-0">
            <Skeleton className="h-14 w-40 rounded" />
            <div className="hidden md:flex flex-col gap-2">
              <Skeleton className="h-6 w-72" />
              <Skeleton className="h-4 w-56" />
            </div>
          </div>

          {/* Right side: contacts + search */}
          <div className="flex flex-1 items-center justify-end gap-8 min-w-0">
            <div className="hidden lg:flex flex-col gap-3 text-right min-w-0">
              <div className="flex items-center gap-4">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-44" />
              </div>
              <div className="flex items-center gap-2">
                <Skeleton className="h-9 w-[310px]" />
              </div>
            </div>
            {/* Mobile menu button */}
            <div className="flex lg:hidden items-center gap-4">
              <Skeleton className="h-6 w-6 rounded-full" />
            </div>
          </div>
        </div>

        {/* Desktop nav bar */}
        <nav className="bg-secondary text-white">
          <div className="max-w-7xl mx-auto px-4 hidden lg:flex items-center justify-between h-14">
            <div className="flex space-x-6">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-14" />
              <Skeleton className="h-4 w-28" />
            </div>
            <Skeleton className="h-9 w-40 rounded" />
          </div>
        </nav>
      </header>
    </div>
  );
}
