import { Badge } from '@/components/ui/badge'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { FileText } from 'lucide-react'
import React from 'react'

const Documentation = () => {
  return (
    <div>
         <section className="w-full py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center space-y-4 mb-12">
              <Badge className="bg-primary/10 text-secondary">Documentation</Badge>
              <h2 className="text-4xl text-center font-bold bg-gradient-to-r from-secondary to-primary bg-clip-text text-transparent mb-2">
                Certification Documentation We Provide
              </h2>
              <p className="mx-auto max-w-3xl text-gray-600 md:text-xl">
                We handle and provide all essential aviation documentation and certificates
              </p>
            </div>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <Card className="border-l-4 border-l-blue-600 shadow-md">
                <CardHeader className="pb-3">
                  <div className="flex items-center space-x-2">
                    <FileText className="h-5 w-5 text-blue-600" />
                    <CardTitle className="text-lg">Certificate of Conformity</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600">Standard certification for parts compliance</p>
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-green-600 shadow-md">
                <CardHeader className="pb-3">
                  <div className="flex items-center space-x-2">
                    <FileText className="h-5 w-5 text-green-600" />
                    <CardTitle className="text-lg">EASA Form 1</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600">European Aviation Safety Agency certification</p>
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-red-600 shadow-md">
                <CardHeader className="pb-3">
                  <div className="flex items-center space-x-2">
                    <FileText className="h-5 w-5 text-red-600" />
                    <CardTitle className="text-lg">FAA 8130</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600">Federal Aviation Administration tag</p>
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-purple-600 shadow-md">
                <CardHeader className="pb-3">
                  <div className="flex items-center space-x-2">
                    <FileText className="h-5 w-5 text-purple-600" />
                    <CardTitle className="text-lg">Transport Canada TC24-0078</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600">Canadian aviation authority certification</p>
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-orange-600 shadow-md">
                <CardHeader className="pb-3">
                  <div className="flex items-center space-x-2">
                    <FileText className="h-5 w-5 text-orange-600" />
                    <CardTitle className="text-lg">CASA DA 1</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600">Civil Aviation Safety Authority (Australia)</p>
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-teal-600 shadow-md">
                <CardHeader className="pb-3">
                  <div className="flex items-center space-x-2">
                    <FileText className="h-5 w-5 text-teal-600" />
                    <CardTitle className="text-lg">CAAN Form One</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600">Civil Aviation Authority Nepal certification</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
    </div>
  )
}

export default Documentation