'use client';

import { ChevronRight } from 'lucide-react';
import Link from 'next/link';

// Sample data (edit as needed!)
const quickLinks: { label: string; href: string }[][] = [
    [
        { label: 'MEET THE TEAM', href: '/about/team' },
        { label: 'CAREERS', href: '/about/careers' },
        { label: 'QUALITY ASSURANCE', href: '/about/qa' },
    ],
    [
        { label: 'CORPORATE SOCIAL RESPONSIBILITY', href: '/about/csr' },
        { label: 'FIND US', href: '/contact/location' },
        { label: 'OUR CAPABILITY', href: '/about/capability' },
    ],
    [
        { label: 'HISTORY', href: '/about/history' },
        { label: 'LOCATIONS', href: '/about/locations' },
        { label: 'SUPPLY CHAIN NETWORK', href: '/about/supply-chain' },
    ],
    [
        { label: '<PERSON><PERSON><PERSON> AEROSPACE GROUP', href: '/about/green-group' },
        { label: 'THE QUEENS AWARD', href: '/about/queens-award' },
    ],
];

export default function QuickLinksSection() {
    return (
        <section className="bg-gradient-to-r from-secondary to-primary py-4">
            <div className="container mx-auto px-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                {quickLinks.map((column, idx) => (
                    <ul key={idx} className="space-y-2">
                        {column.map((item) => (
                            <li key={item.label}>
                                <Link
                                    href={item.href}
                                    className="group flex items-center gap-2 text-white font-medium hover:text-white/90 transition"
                                >
                                    <ChevronRight
                                        className="w-5 h-5 transform transition-transform duration-200 group-hover:translate-x-2"
                                    />
                                    <span>{item.label}</span>
                                </Link>
                            </li>
                        ))}
                    </ul>
                ))}
            </div>
        </section>
    );
}
