"use client";

import { IBlogPost } from "@/types/blog";
import { BlogCard } from "@/components/blog/card";

export default function BlogPageClient({ initialPosts }: { initialPosts: IBlogPost[] }) {
    const posts = initialPosts ?? [];

    if (posts.length === 0) {
        return (
            <div className="container mx-auto px-4 py-10 pt-20 text-center text-gray-700">
                <p className="text-xl font-semibold mb-2">No Blog Posts Found</p>
                <p className="text-gray-500 max-w-xl mx-auto">
                    We couldn&apos;t find any blog posts matching your criteria at the moment.
                    Please check back later or explore other sections of our website.
                </p>
            </div>
        );
    }

    return (
        <div className="container mx-auto px-4 pt-20">
            <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
                {posts.map((post) => (
                    <BlogCard key={post.id} post={post} />
                ))}

            </div>
        </div>
    );
}
