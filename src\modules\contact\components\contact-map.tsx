import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import React from 'react'

const ContactMap = () => {
    return (
        <div className="container mx-auto px-4">
            <div className="mt-12">
                <Card className="border-0 shadow-lg overflow-hidden">
                    <CardHeader className="pb-0">
                        <CardTitle className="text-2xl text-center">Find Us on Google Maps</CardTitle>
                    </CardHeader>
                    <CardContent className="p-0 mt-4">
                        <div className="h-96 w-full">
                            <iframe
                                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d51144.287824305415!2d85.28493293612321!3d27.709030241926197!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39eb198a307baabf%3A0xb5137c1bf18db1ea!2sKathmandu%2044600!5e1!3m2!1sen!2snp!4v1755511748813!5m2!1sen!2snp"
                                width="100%"
                                height="100%"
                                className="border-none"
                                allowFullScreen={true}
                                referrerPolicy="no-referrer-when-downgrade"
                                title="Trek and Trail Location"
                            />
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    )
}

export default ContactMap