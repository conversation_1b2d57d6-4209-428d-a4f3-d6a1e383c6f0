// 'use client';

// import { useState, useMemo } from 'react';
// import Link from 'next/link';
// import { Loader2, ImageIcon } from 'lucide-react';
// import { Button } from '@/components/ui/button';
// import { Card } from '@/components/ui/card';

// import getProduct from '@/actions/product/get-product';
// import getSubCategory from '@/actions/subcategory/get-subcategory';
// import getCategory from '@/actions/category/get-category';

// import type { IProduct, ISubCategory, ICategory } from '@/types/category';
// import { SearchBar } from './search-bar';
// import Image from 'next/image';

// type ProductWithRelations = IProduct & {
//   description?: string | null;
//   image?: string[] | string | null;
//   subCategoryId?: string | null;
//   subCategory?: ISubCategory | null;
// };

// type Hit = {
//   product: ProductWithRelations;
//   url: string;
//   matchedField: 'name' | 'slug' | 'description';
// };

// const stripHtml = (html?: string | null): string =>
//   (html ?? '').replace(/<[^>]+>/g, ' ').replace(/\s+/g, ' ').trim();

// const buildWildcardPattern = (q: string): RegExp =>
//   q.includes('*')
//     ? new RegExp(q.replace(/[.*+?^${}()|[\]\\]/g, '\\$&').replace(/\\\*/g, '.*'), 'i')
//     : new RegExp(q, 'i');

// const toAbsPath = (...parts: (string | undefined | null)[]): string =>
//   '/' +
//   parts
//     .filter((p): p is string => Boolean(p && p.trim()))
//     .map((s) => s.trim().replace(/^\/+|\/+$/g, ''))
//     .join('/');

// const firstImage = (p: ProductWithRelations): string | undefined => {
//   const img = p.image;
//   if (!img) return undefined;
//   if (Array.isArray(img)) return img[0];
//   return img; // string
// };

// export default function PartSearchSection() {
//   const [hits, setHits] = useState<Hit[]>([]);
//   const [loading, setLoading] = useState(false);
//   const [ran, setRan] = useState(false);

//   const onSearch = async (query: string) => {
//     const q = query.trim();
//     setRan(true);
//     if (!q) {
//       setHits([]);
//       return;
//     }

//     setLoading(true);
//     try {
//       const [prodRes, subRes, catRes] = await Promise.all([
//         getProduct(),
//         getSubCategory(),
//         getCategory(),
//       ]);

//       const products = (prodRes?.data ?? []) as ProductWithRelations[];
//       const subs = (subRes?.data ?? []) as ISubCategory[];
//       const cats = (catRes?.data ?? []) as ICategory[];

//       const subById = new Map<string, ISubCategory>(
//         subs.map((s) => [s.id as string, s]),
//       );
//       const catById = new Map<string, ICategory>(
//         cats.map((c) => [c.id as string, c]),
//       );

//       const pattern = buildWildcardPattern(q);
//       const results: Hit[] = [];

//       for (const p of products) {
//         const name = p.name ?? '';
//         const slug = p.slug ?? '';
//         const desc = stripHtml(p.description);

//         const byName = name && pattern.test(name);
//         const bySlug = !byName && slug && pattern.test(slug);
//         const byDesc = !byName && !bySlug && desc && pattern.test(desc);
//         if (!byName && !bySlug && !byDesc) continue;

//         let url = '#';
//         if (slug) {
//           const subInline = p.subCategory ?? undefined;
//           const sub = subInline ?? (p.subCategoryId ? subById.get(p.subCategoryId) : undefined);
//           const category = sub?.categoryId ? catById.get(sub.categoryId) : undefined;

//           url = category?.slug ? toAbsPath(category.slug, slug) : toAbsPath('product', slug);
//         }

//         results.push({
//           product: p,
//           url,
//           matchedField: byName ? 'name' : bySlug ? 'slug' : 'description',
//         });
//       }

//       results.sort((a, b) => {
//         const rank = (f: Hit['matchedField']) => (f === 'name' ? 0 : f === 'slug' ? 1 : 2);
//         const r = rank(a.matchedField) - rank(b.matchedField);
//         if (r !== 0) return r;
//         return (a.product.name || '').localeCompare(b.product.name || '');
//       });

//       setHits(results);
//     } catch (e) {
//       console.error('Product search error:', e);
//       setHits([]);
//     } finally {
//       setLoading(false);
//     }
//   };

//   const resultsUI = useMemo(() => {
//     if (loading) {
//       return (
//         <div className="flex items-center gap-2 text-muted-foreground">
//           <Loader2 className="h-4 w-4 animate-spin" />
//           Searching…
//         </div>
//       );
//     }
//     if (!hits.length) {
//       return ran ? <div className="text-sm text-muted-foreground">No matches found.</div> : null;
//     }

//     return (
//       <div className="grid gap-4">
//         {hits.map(({ product, url, matchedField }) => {
//           const img = firstImage(product);
//           const sub = product.subCategory ?? undefined;
//           return (
//             <Card key={product.id} className="p-4 hover:shadow-md transition">
//               <div className="flex items-center gap-4">
//                 <div className="w-16 h-16 rounded-md overflow-hidden bg-muted flex items-center justify-center shrink-0">
//                   {img ? (
//                     <Image
//                       src={img}
//                       alt={product.name}
//                       width={50}
//                       height={50}
//                       className="w-full h-full object-cover"
//                     />
//                   ) : (
//                     <ImageIcon className="w-6 h-6 text-muted-foreground" />
//                   )}
//                 </div>

//                 <div className="min-w-0 flex-1">
//                   <div className="text-xs text-muted-foreground">
//                     {sub?.name ? `${sub.name} • ` : ''}
//                     {product.slug}
//                   </div>
//                   <div className="font-medium truncate">{product.name}</div>
//                   <div className="mt-1 text-[11px] uppercase tracking-wide text-primary">
//                     Matched by {matchedField}
//                   </div>
//                 </div>

//                 <Button asChild>
//                   <Link href={url}>View</Link>
//                 </Button>
//               </div>
//             </Card>
//           );
//         })}
//       </div>
//     );
//   }, [hits, loading, ran]);

//   return (
//     <section className="w-full">
//       <SearchBar onSearch={onSearch} />
//       <div className="container mx-auto px-4">{resultsUI}</div>
//     </section>
//   );
// }
