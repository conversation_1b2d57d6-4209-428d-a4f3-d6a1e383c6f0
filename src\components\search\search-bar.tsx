"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";
import {
  Select, SelectTrigger, SelectValue, SelectContent, SelectItem,
} from "@/components/ui/select";

export default function InlineSearchBar({ defaultQuery = "" }: { defaultQuery?: string }) {
  const router = useRouter();
  const sp = useSearchParams();
  const [q, setQ] = useState(defaultQuery);
  const [field, setField] = useState(sp.get("field") ?? "name");

  useEffect(() => {
    setField(sp.get("field") ?? "name");
  }, [sp]);

  const onSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const query = q.trim();
    if (!query) return;
    router.push(`/search?query=${encodeURIComponent(query)}&field=${encodeURIComponent(field)}`);
  };

  return (
    <form onSubmit={onSubmit} className="flex flex-col md:flex-row gap-2 w-full md:w-[620px]">
      <div className="flex items-center gap-2">
        <Select value={field} onValueChange={setField}>
          <SelectTrigger className="w-[160px]">
            <SelectValue placeholder="Field" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="name">Name</SelectItem>
            <SelectItem value="slug">Slug</SelectItem>
          </SelectContent>
        </Select>
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            value={q}
            onChange={(e) => setQ(e.target.value)}
            placeholder={`Search in ${field}… (supports *)`}
            className="pl-9"
            spellCheck={false}
          />
        </div>
      </div>
      <Button type="submit" disabled={!q.trim()}>Search</Button>
    </form>
  );
}
